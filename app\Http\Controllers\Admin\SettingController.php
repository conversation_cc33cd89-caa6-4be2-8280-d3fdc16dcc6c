<?php

namespace App\Http\Controllers\Admin;

use App\Traits\Report;
use App\Models\SiteSetting;
use Illuminate\Http\Request;
use App\Services\SettingService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Cache;
use App\Models\Country ;

class SettingController extends Controller
{
    public function index(){
        $data = Cache::rememberForever('settings', function () {
            return SettingService::appInformations(SiteSetting::pluck('value', 'key'));
        });
        $countries = Country::orderBy('id','ASC')->get();
        return view('admin.settings.index',compact('data','countries'));
    }


    public function update(Request $request){
        Cache::forget('settings');

        // Process all request data
        foreach ($request->all() as $key => $val) {
            // Handle image uploads using Spatie Media Library
            if (in_array($key, ['logo', 'fav_icon', 'default_user', 'intro_loader', 'intro_logo', 'about_image_2', 'about_image_1', 'login_background', 'profile_cover', 'no_data'])) {
                if ($val && is_file($val)) {
                    // Get or create the setting record
                    $setting = SiteSetting::firstOrCreate(['key' => $key], ['key' => $key, 'value' => '']);

                    // Clear existing media in this collection
                    $setting->clearMediaCollection($key);

                    // Determine filename based on key
                    $fileName = match($key) {
                        'default_user' => 'default.png',
                        'no_data' => 'no_data.png',
                        default => time() . '_' . $key . '.' . $val->getClientOriginalExtension()
                    };

                    // Add the new media to the collection
                    $media = $setting->addMediaFromRequest($key)
                        ->usingName($key)
                        ->usingFileName($fileName)
                        ->toMediaCollection($key);

                    // Update the value field with the media URL
                    $setting->update(['value' => $media->getUrl()]);
                }
            }
            // Handle non-file inputs
            else if ($val) {
                SiteSetting::updateOrCreate(['key' => $key], ['key' => $key, 'value' => $val]);
            }
        }

        // Handle boolean settings
        if ($request->is_production) {
            SiteSetting::where('key', 'is_production')->update(['value' => 1]);
        } else {
            SiteSetting::where('key', 'is_production')->update(['value' => 0]);
        }

        if ($request->registeration_availability) {
            SiteSetting::where('key', 'registeration_availability')->update(['value' => 1]);
        } else {
            SiteSetting::where('key', 'registeration_availability')->update(['value' => 0]);
        }

        // Refresh the cache
        Cache::rememberForever('settings', function () {
            return SettingService::appInformations(SiteSetting::pluck('value', 'key'));
        });

        Report::addToLog('تعديل الاعدادت');
        return back()->with('success', 'تم الحفظ');
    }


    public function messageAll(Request $request,$type){

        $this->userRepo->messageAll($request->all(),$type);
        return back()->with('success','تم الارسال');
    }

    public function messageOne(Request $request,$type){

        $this->userRepo->messageOne($request->all(),$type);
        return back()->with('success','تم الارسال');
    }

    public function sendEmail(Request $request){

        $this->settingRepo->sendEmail($request->all());
        return back()->with('success','تم الارسال');
    }
}
