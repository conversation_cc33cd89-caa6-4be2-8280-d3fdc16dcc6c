<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" type="text/css"
        href="<?php echo e(asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')); ?>">
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>
<!-- // Basic multiple Column Form section start -->
<section id="multiple-column-form">
    <div class="row match-height">
        <div class="col-12">
            <div class="card">
                
                <div class="card-content">
                    <div class="card-body">
                        <form  method="POST" action="<?php echo e(route('admin.productcategories.store')); ?>" class="store form-horizontal" novalidate>
                            <?php echo csrf_field(); ?>
                            <div class="form-body">
                                <div class="row">
                                   
                                    
                                    

                                        <div class="col-12">
                                            <div class="imgMontg col-12 text-center">
                                                <div class="dropBox">
                                                    <div class="textCenter">
                                                        <div class="imagesUploadBlock">
                                                            <label class="uploadImg">
                                                                <span><i class="feather icon-image"></i></span>
                                                                <input type="file" accept="image/*" name="image" class="imageUploader">
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                    
                                    

                                            <div class="col-md-6 col-12">
                                                <div class="form-group">
                                                    <label for="first-name-column"><?php echo e(__('admin.name')); ?></label>
                                                    <div class="controls">
                                                        <input type="text" name="name" class="form-control"
                                                            placeholder="<?php echo e(__('admin.name')); ?>" required
                                                            data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6 col-12">
                                                <div class="form-group">
                                                    <label for="first-name-column"><?php echo e(__('admin.phone')); ?></label>
                                                    <div class="controls">
                                                        <input type="number" name="phone" class="form-control"
                                                            placeholder="<?php echo e(__('admin.phone')); ?>" required
                                                            data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6 col-12">
                                                <div class="form-group">
                                                    <label for="first-name-column"><?php echo e(__('admin.email')); ?></label>
                                                    <div class="controls">
                                                        <input type="email" name="email" class="form-control"
                                                            placeholder="<?php echo e(__('admin.email')); ?>" required
                                                            data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6 col-12">
                                                <div class="form-group">
                                                    <label for="first-name-column"><?php echo e(__('admin.password')); ?></label>
                                                    <div class="controls">
                                                        <input type="password" name="password" class="form-control" required
                                                            data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-12">
                                                <div class="form-group">
                                                    <div class="controls">
                                                        <label for="account-name"><?php echo e(__('admin.about_app')); ?></label>
                                                        <textarea class="form-control" name="intro_about" id="" cols="30" rows="10"
                                                            placeholder="<?php echo e(__('admin.about_app')); ?>"></textarea>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="col-md-12 col-12">
                                                <div class="form-group">
                                                    <label for="first-name-column"><?php echo e(__('admin.ban_status')); ?></label>
                                                    <div class="controls">
                                                        <select name="block" class="select2 form-control" required
                                                            data-validation-required-message="<?php echo e(__('admin.this_field_is_required')); ?>">
                                                            <option value><?php echo e(__('admin.Select_the_blocking_status')); ?></option>
                                                            <option value="1"><?php echo e(__('admin.Prohibited')); ?></option>
                                                            <option value="0"><?php echo e(__('admin.Unspoken')); ?></option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            
                                        
                                    
                                    



                                        <div class="col-12 d-flex justify-content-center mt-3">
                                            <button type="submit"
                                                class="btn btn-primary mr-1 mb-1 submit_button"><?php echo e(__('admin.add')); ?></button>
                                            <a href="<?php echo e(url()->previous()); ?>" type="reset"
                                                class="btn btn-outline-warning mr-1 mb-1"><?php echo e(__('admin.back')); ?></a>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>
<?php $__env->startSection('js'); ?>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/forms/validation/form-validation.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')); ?>"></script>

    
    <?php echo $__env->make('admin.shared.addImage', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    

    
    <?php echo $__env->make('admin.shared.submitAddForm', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\sorriso-backend\resources\views/admin/productcategories/create.blade.php ENDPATH**/ ?>