<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class VideoProgress extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'course_id',
        'stage_id',
        'current_time',
        'duration',
        'progress_percentage',
        'is_completed',
    ];

    protected $casts = [
        'current_time' => 'float',
        'duration' => 'float',
        'progress_percentage' => 'float',
        'is_completed' => 'boolean',
    ];

    /**
     * Get the user that owns the progress.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the course that owns the progress.
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Course::class);
    }

    /**
     * Get the stage that owns the progress.
     */
    public function stage(): BelongsTo
    {
        return $this->belongsTo(CourseStage::class, 'stage_id');
    }

    /**
     * Get formatted progress percentage
     */
    public function getFormattedProgressAttribute(): string
    {
        return round($this->progress_percentage, 1) . '%';
    }

    /**
     * Get formatted current time
     */
    public function getFormattedCurrentTimeAttribute(): string
    {
        $minutes = floor($this->current_time / 60);
        $seconds = $this->current_time % 60;
        
        return sprintf('%02d:%02d', $minutes, $seconds);
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute(): string
    {
        $minutes = floor($this->duration / 60);
        $seconds = $this->duration % 60;
        
        return sprintf('%02d:%02d', $minutes, $seconds);
    }
}
