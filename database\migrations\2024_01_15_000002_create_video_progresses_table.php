<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('video_progresses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('course_id')->constrained()->onDelete('cascade');
            $table->foreignId('stage_id')->constrained('course_stages')->onDelete('cascade');
            $table->float('current_time')->default(0); // Current playback time in seconds
            $table->float('duration')->default(0); // Total video duration in seconds
            $table->float('progress_percentage')->default(0); // Percentage of video watched
            $table->boolean('is_completed')->default(false); // Whether video is fully watched
            $table->timestamps();

            // Indexes
            $table->index(['user_id', 'course_id']);
            $table->index(['user_id', 'stage_id']);
            $table->unique(['user_id', 'course_id', 'stage_id']); // One progress record per user per stage
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('video_progresses');
    }
};
