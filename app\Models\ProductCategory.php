<?php

namespace App\Models;

use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Spatie\MediaLibrary\InteractsWithMedia;

class ProductCategory extends BaseModel implements HasMedia
{
    use HasTranslations , InteractsWithMedia;

    protected $fillable = ['name','is_active'];
    public $translatable = ['name'];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('product-categories')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif', 'image/webp'])
            ->useFallbackUrl(asset('storage/images/product-categories/default.png'))
            ->useFallbackPath(public_path('storage/images/product-categories/default.png'));
    }

    /**
     * Register media conversions for product categories
     */
    public function registerMediaConversions($media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(150)
            ->height(150)
            ->nonQueued();

        $this->addMediaConversion('medium')
            ->width(500)
            ->height(500)
            ->nonQueued();
    }

    /**
     * Get product category image URL
     */
    public function getImageUrlAttribute()
    {
        return $this->getFirstMediaUrl('product-categories');
    }

    /**
     * Get product category image URL with conversion
     */
    public function getImageUrl($conversion = null)
    {
        if ($conversion) {
            return $this->getFirstMediaUrl('product-categories', $conversion);
        }
        return $this->getFirstMediaUrl('product-categories');
    }

    /**
     * Get the products for the category
     */
    public function products()
    {
        return $this->hasMany(\App\Models\Product::class);
    }

    public function getProductsCountAttribute()
    {
        return $this->products()->count();
    }

}
